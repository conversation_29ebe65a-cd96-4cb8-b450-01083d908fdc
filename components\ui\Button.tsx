'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ReactNode } from 'react';

// Component props interface
interface ButtonProps {
  variant?: 'primary' | 'outline';
  onClick?: () => void;
  href?: string;
  children: ReactNode;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

// Reusable Button Component with Framer Motion animations
export default function Button({
  variant = 'primary',
  onClick,
  href,
  children,
  className = '',
  type = 'button',
  disabled = false,
}: ButtonProps) {
  // Base styles for all buttons
  const baseStyles = `
    inline-flex items-center justify-center
    px-6 py-3 rounded-lg font-medium text-sm
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background
    disabled:opacity-50 disabled:cursor-not-allowed
    ${className}
  `;

  // Variant-specific styles
  const variantStyles = {
    primary: `
      bg-accent text-background
      hover:bg-accent/90
      border border-accent
    `,
    outline: `
      bg-transparent text-accent
      border border-accent
      hover:bg-accent hover:text-background
    `,
  };

  // Combined styles
  const buttonStyles = `${baseStyles} ${variantStyles[variant]}`.trim();

  // Animation props for Framer Motion
  const animationProps = {
    whileHover: disabled ? {} : { 
      scale: 1.05, 
      boxShadow: '0 0 8px rgba(100, 255, 218, 0.5)' 
    },
    whileTap: disabled ? {} : { 
      scale: 0.95 
    },
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 17,
    },
  };

  // If href is provided, render as a Link
  if (href) {
    return (
      <Link href={href} className="inline-block">
        <motion.button
          className={buttonStyles}
          disabled={disabled}
          {...animationProps}
        >
          {children}
        </motion.button>
      </Link>
    );
  }

  // Otherwise, render as a regular button
  return (
    <motion.button
      type={type}
      onClick={onClick}
      className={buttonStyles}
      disabled={disabled}
      {...animationProps}
    >
      {children}
    </motion.button>
  );
}
