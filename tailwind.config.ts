import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: '#0a192f', // A dark navy blue
        textPrimary: '#ccd6f6', // A light, muted blue/grey
        textSecondary: '#8892b0', // A darker grey for secondary text
        accent: '#64ffda', // A bright teal/mint for highlights and CTAs
        card: '#112240', // A slightly lighter navy for card backgrounds
      },
      fontFamily: {
        sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
}

export default config
