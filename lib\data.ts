// Data & Asset Manifest - Single source of truth for all portfolio content
// This file contains all the structured data used throughout the portfolio application

// Type definitions for data structures
export interface HeroData {
  name: string;
  bio: string;
  profileImage: string;
  buttons: {
    viewWork: string;
    viewResume: string;
  };
  resumeLink: string;
  socialLinks: {
    github: string;
    linkedin: string;
    twitter: string;
  };
}

export interface WorkExperience {
  id: string;
  companyName: string;
  companyLogo: string;
  role: string;
  timeline: string;
  details: string[];
  certificateLink: string;
}

export interface Project {
  id: string;
  name: string;
  purpose: string;
  techStack: string;
  learnings: string;
  githubLink: string;
  liveLink?: string;
}

export interface Certificate {
  id: string;
  name: string;
  issuerName: string;
  issuerLogo: string;
}

export interface Education {
  degree: string;
  institution: string;
  timeline: string;
  detail: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
}

export interface FooterInfo {
  name: string;
}

// Hero Section Data (HERO-01 to HERO-09)
export const heroData: HeroData = {
  name: "<PERSON><PERSON><PERSON>", // HERO-01
  bio: "A skilled and passionate developer with experience in creating modern, responsive, and scalable web applications using React, Next.js, and AI/ML technologies.", // HERO-02
  profileImage: "/assets/images/Profile.jpeg", // HERO-03
  buttons: {
    viewWork: "View Work", // HERO-04
    viewResume: "View Resume", // HERO-05
  },
  resumeLink: "/assets/docs/Resume_Janmejay_Tiwari", // HERO-06
  socialLinks: {
    github: "https://github.com/Janmejay3108", // HERO-07
    linkedin: "https://linkedin.com/in/janmejay-tiwari", // HERO-08
    twitter: "https://x.com/Janmeja_y", // HERO-09
  },
};

// Work Experience Data (EXP-01 to EXP-11)
export const workExperience: WorkExperience[] = [
  {
    id: "exp-01",
    companyName: "Celebal Technologies", // EXP-01
    companyLogo: "/assets/images/logo_celebal.svg", // EXP-02
    role: "React Developer Intern", // EXP-03
    timeline: "May 2025 - July 2025", // EXP-04
    details: [ // EXP-05
      "Worked on the development of a real-time chat application for Bank of Baroda, enhancing customer support and engagement.",
      "Built responsive, component-based UIs using React.js, JavaScript, and Tailwind CSS."
    ],
    certificateLink: "https://drive.google.com/file/d/15YBIy42FxAL-MBu8AApibsSMeBCZ208e/view?usp=sharing", // EXP-06
  },
  {
    id: "exp-02",
    companyName: "1stop.ai", // EXP-07
    companyLogo: "/assets/images/logo_1stopai.svg", // EXP-08
    role: "Frontend developer Intern", // EXP-09
    timeline: "March 2024 - April 2024.", 
    details: [ // EXP-10
      "Collaborated with a team to develop responsive web application using MERN stack.",
      "Implemented frontend components using React.js.",
      "Assisted in database design and implementation with Firebase.",
      "Integrated AWS S3 for media storage and content delivery."
    ],
    certificateLink: "https://drive.google.com/file/d/1Hi-eA_mT2DMZPnIUgGvEWqbPAtC50tKs/view?usp=sharing", // EXP-11
  },
];

// Project Work Data (PROJ-01 to PROJ-10)
export const projects: Project[] = [
  {
    id: "proj-01",
    name: "Web accessibility analyser", // PROJ-01
    purpose: "A platform that analyses websites for accessibility issues and provides AI-powered remediation suggestions.", // PROJ-02
    techStack: "", // PROJ-03 (empty in manifest)
    learnings: "Learned to integrate third-party accessibility testing engines and leverage AI for providing actionable feedback to developers.", // PROJ-04
    githubLink: "https://github.com/Janmejay3108/Accessibility-analyzer", // PROJ-05
  },
  {
    id: "proj-02",
    name: "Secure File Sharing Platform", // PROJ-06
    purpose: "A full-stack platform for transferring files up to 100MB with single-use codes, i18n support, and drag-and-drop UI.", // PROJ-07
    techStack: "", // PROJ-08 (empty in manifest)
    learnings: "Gained experience in building end-to-end applications with secure file handling, internationalization, and containerization with Docker.", // PROJ-09
    githubLink: "https://github.com/Janmejay3108/File_Transfer_application", // PROJ-10
  },
];

// Certificates Data (CERT-01 to CERT-18)
export const certificates: Certificate[] = [
  {
    id: "cert-01",
    name: "AWS Cloud Solutions Architect", // CERT-01
    issuerName: "Amazon Web Services", // CERT-02
    issuerLogo: "/assets/images/logo_aws.svg", // CERT-03
  },
  {
    id: "cert-02",
    name: "Full Stack Developer", // CERT-04
    issuerName: "IBM", // CERT-05
    issuerLogo: "/assets/images/logo_ibm.svg", // CERT-06
  },
  {
    id: "cert-03",
    name: "Database and SQL for data science with Python", // CERT-07
    issuerName: "IBM", // CERT-08
    issuerLogo: "/assets/images/logo_ibm.svg", // CERT-09
  },
  {
    id: "cert-04",
    name: "RAG and Agentic AI", // CERT-10
    issuerName: "IBM", // CERT-11
    issuerLogo: "/assets/images/logo_ibm.svg", // CERT-12
  },
  {
    id: "cert-05",
    name: "Artificial Intelligence Essentials", // CERT-13
    issuerName: "Google", // CERT-14
    issuerLogo: "/assets/images/logo_google.svg", // CERT-15
  },
  {
    id: "cert-06",
    name: "Convolutional Neural Networks", // CERT-16
    issuerName: "Deeplearning.AI", // CERT-17
    issuerLogo: "/assets/images/logo_deeplearning_ai.svg", // CERT-18
  },
];

// Education Data (EDU-01 to EDU-04)
export const education: Education = {
  degree: "BTech in Electronics and Communication Engineering", // EDU-01
  institution: "Institute of Engineering and Management, Kolkata", // EDU-02
  timeline: "2022-2026", // EDU-03
  detail: "CGPA: 9.38", // EDU-04
};

// Contact Information (CONTACT-01 to CONTACT-02)
export const contactInfo: ContactInfo = {
  email: "<EMAIL>", // CONTACT-01
  phone: "+91 9163083482", // CONTACT-02
};

// Footer Information (FOOTER-01)
export const footerInfo: FooterInfo = {
  name: "Janmejay Tiwari", // FOOTER-01
};
